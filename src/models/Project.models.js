import { DataTypes, Sequelize } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const Project = sequelize.define(
  'Project',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 200]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    instructions: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Detailed instructions for the project'
    },
    course_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'courses',
        key: 'id'
      }
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.ENUM('draft', 'published', 'archived'),
      defaultValue: 'draft'
    },
    difficulty_level: {
      type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
      defaultValue: 'beginner'
    },
    estimated_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Estimated completion time in hours'
    },
    notebook_template_s3_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'S3 URL for the notebook template'
    },
    dataset_s3_url: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
      comment: 'Array of S3 URLs for project dataset files'
    },
    additional_files_s3_urls: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
      comment: 'Array of S3 URLs for additional project files'
    },
    start_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    due_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    late_submission_allowed: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    late_penalty_percent: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0,
      comment: 'Percentage penalty per day late (0-100)'
    },
    max_attempts: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      comment: 'Maximum number of submission attempts allowed'
    },
    auto_grading_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    learning_objectives: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'learning objectives for the project'
    },
    prerequisites: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Prerequisite topics or projects'
    },
    tags: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
      comment: 'Array of tags for categorization'
    },
    settings: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Project-specific settings and configurations'
    },
    isScreen: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      field: 'isScreen',
      validate: {
        isIn: {
          args: [[1, 2, 3, 4]],
          msg: 'isScreen must be one of: 1, 2, 3, 4'
        }
      },
      comment: 'Screen number for project (1-4)'
    },
    project_code: {
      type: DataTypes.STRING(6), // or DataTypes.CHAR(6)
      allowNull: false,
      unique: true,
      field: 'project_code',
      validate: { is: /^[0-9]{6}$/ },
      // 👇 ensures Sequelize doesn’t complain and uses the DB sequence
      defaultValue: Sequelize.literal(
        "lpad(nextval('projects_project_code_seq')::text, 6, '0')"
      ),
      comment: 'Public 6-digit id like 000001'
    },
    category_id: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: 'category UUIDs'
    },
    instructor_id: {
      type: DataTypes.ARRAY(DataTypes.UUID),
      allowNull: true,
      defaultValue: [],
      comment: 'Array of instructor UUIDs'
    },
    teaching_ass_id: {
      type: DataTypes.ARRAY(DataTypes.UUID),
      allowNull: true,
      defaultValue: [],
      field: 'teaching_ass_id',
      comment: 'Array of teaching assistant UUIDs'
    },
    total_points: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'total points for the project'
    },
    project_overview: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Detailed overview for the project'
    },
    type: {
      type: DataTypes.ENUM(
        'group',
        'individual',
        'research',
        'competition',
        'tutorial'
      ),
      defaultValue: 'group'
    },
    sandbox_time_duration: {
      type: DataTypes.STRING(6),
      allowNull: true,
      validate: {
        is: /^(\d{1,3}):([0-5]\d)$/ // 000:00 to 023:59
      },
      comment: 'Allowed sandbox duration in HHH:MM format'
    },
    late_submission_days_allowed: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Number of days late submission is allowed'
    }
  },
  {
    tableName: 'projects',
    indexes: [
      {
        fields: ['course_id']
      },
      {
        fields: ['created_by']
      },
      {
        fields: ['status']
      },
      {
        fields: ['difficulty_level']
      },
      {
        fields: ['due_date']
      }
    ]
  }
);

export default Project;
