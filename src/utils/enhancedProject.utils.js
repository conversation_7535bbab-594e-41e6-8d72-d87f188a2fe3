import logger from '../config/logger.config.js';
import { Project } from '../models/associations.js';
import ApiError from './ApiError.utils.js';
import httpStatus from 'http-status';

class enhancedProjectUtils {
  /**
   * check if project is exits or not
   */
  async checkProjectExist(projectId) {
    try {
      const project = await Project.findByPk(projectId);
      if (!project) {
        return false;
      }
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * update project details
   */
  async updateProjectDetails(projectId, updates, transaction = null) {
    try {
      const project = await Project.findByPk(projectId, { transaction });
      if (!project) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }
      await project.update(updates, { transaction });
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * update a project's dataset S3 URLs
   * @param {string|string[]} s3Urls - The S3 URL(s) of the dataset
   * @param {string} projectId - The ID of the project to update
   * @returns {Promise<void>}
   */
  async updateDataSetS3Url(s3Urls, projectId) {
    try {
      const project = await Project.findByPk(projectId);
      if (!project) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }
      project.dataset_s3_url = Array.isArray(s3Urls) ? s3Urls : [s3Urls];
      await project.save();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a project belongs to a specific course
   * @param {UUID} courseId
   * @param {UUID} projectId
   * @returns
   */
  async checkCourseProjectId(courseId, projectId) {
    try {
      const where = {
        id: projectId
      };
      if (courseId) {
        where.course_id = courseId;
      }
      const project = await Project.findOne({
        where
      });
      if (!project) {
        if (courseId) {
          logger.error(
            `Project ${projectId} does not belong to course ${courseId}`
          );
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            'Project does not belong to the specified course'
          );
        }
        logger.error(`Project ${projectId} not found`);
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }
      return true;
    } catch (error) {
      throw error;
    }
  }
}

export default new enhancedProjectUtils();
