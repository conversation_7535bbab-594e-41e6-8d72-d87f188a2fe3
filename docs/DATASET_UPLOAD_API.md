# Dataset Upload API Documentation

## Overview
The Dataset Upload API provides comprehensive file management for project datasets including upload, delete, and replace operations.

## Endpoint
```
POST /api/s3/upload-project-dataset
```

## Authentication
- **Required**: JWT Token
- **Permissions**: `edit_projects`

---

## Request Parameters

### Form Data Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `projectId` | UUID | ✅ | Project identifier |
| `courseId` | UUID | ❌ | Course identifier |
| `isPublic` | Boolean | ❌ | Make files public (default: false) |
| `isDelete` | Boolean | ❌ | Delete all existing files |
| `filesToDelete` | JSON Array | ❌ | Specific file keys to delete |
| `isUploadFils` | Boolean | ❌ | Enable file upload (default: false) |
| `dataset` | File Array | ❌ | Files to upload |

### File Constraints
- **Allowed Types**: `.csv`, `.json`, `.zip`, `.parquet`, `.ipynb`
- **Max Size**: 200MB per file
- **Max Files**: 100 files per request

---

## Use Cases

### 1. Upload New Files
Upload new dataset files to a project.

**Request:**
```bash
curl -X POST /api/s3/upload-project-dataset \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "projectId=123e4567-e89b-12d3-a456-************" \
  -F "isUploadFils=true" \
  -F "dataset=@data.csv" \
  -F "dataset=@config.json"
```

**Response:**
```json
{
  "success": true,
  "message": "Files uploaded successfully",
  "data": [
    {
      "url": "https://s3.amazonaws.com/bucket/project-dataset/user123/data.csv",
      "key": "project-dataset/user123/data.csv",
      "bucket": "your-bucket",
      "size": 1024,
      "contentType": "text/csv",
      "fileName": "data.csv",
      "isPublic": false,
      "downloadUrl": "https://presigned-url..."
    }
  ]
}
```

### 2. Delete Specific Files
Remove specific files by their S3 keys.

**Request:**
```bash
curl -X POST /api/s3/upload-project-dataset \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "projectId=123e4567-e89b-12d3-a456-************" \
  -F 'filesToDelete=["project-dataset/user123/old-file.csv"]' \
  -F "isUploadFils=false"
```

**Response:**
```json
{
  "success": true,
  "message": "Files processed successfully",
  "data": {
    "message": "1 files deleted successfully",
    "files": []
  }
}
```

### 3. Delete All Files
Remove all dataset files from a project.

**Request:**
```bash
curl -X POST /api/s3/upload-project-dataset \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "projectId=123e4567-e89b-12d3-a456-************" \
  -F "isDelete=true"
```

**Response:**
```json
{
  "success": true,
  "message": "Files processed successfully",
  "data": {
    "message": "All dataset files deleted successfully"
  }
}
```

### 4. Replace Files (Delete + Upload)
Delete specific files and upload new ones in a single request.

**Request:**
```bash
curl -X POST /api/s3/upload-project-dataset \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "projectId=123e4567-e89b-12d3-a456-************" \
  -F 'filesToDelete=["project-dataset/user123/old-data.csv"]' \
  -F "isUploadFils=true" \
  -F "dataset=@new-data.csv"
```

**Response:**
```json
{
  "success": true,
  "message": "Files uploaded successfully",
  "data": [
    {
      "url": "https://s3.amazonaws.com/bucket/project-dataset/user123/new-data.csv",
      "key": "project-dataset/user123/new-data.csv",
      "size": 2048,
      "fileName": "new-data.csv",
      "isPublic": false
    }
  ]
}
```

### 5. Delete Only (No Upload)
Delete files without uploading new ones.

**Request:**
```bash
curl -X POST /api/s3/upload-project-dataset \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "projectId=123e4567-e89b-12d3-a456-************" \
  -F 'filesToDelete=["file1.csv", "file2.json"]' \
  -F "isUploadFils=false"
```

---

## Error Responses

### Validation Errors
```json
{
  "success": false,
  "error": "Validation Error",
  "message": "Valid project ID is required"
}
```

### File Type Error
```json
{
  "success": false,
  "error": "Bad Request", 
  "message": "File type not allowed. Allowed types: csv, json, zip, parquet, ipynb"
}
```

### File Size Error
```json
{
  "success": false,
  "error": "Bad Request",
  "message": "File size too large. Maximum size: 200MB"
}
```

### No Files Found
```json
{
  "success": false,
  "error": "Not Found",
  "message": "No dataset files to delete"
}
```

---

## Frontend Implementation

### JavaScript/React Example

```javascript
class DatasetManager {
  constructor(apiBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
  }

  // Upload new files
  async uploadFiles(projectId, files, options = {}) {
    const formData = new FormData();
    formData.append('projectId', projectId);
    formData.append('isUploadFils', 'true');
    
    if (options.courseId) formData.append('courseId', options.courseId);
    if (options.isPublic) formData.append('isPublic', options.isPublic);
    
    files.forEach(file => formData.append('dataset', file));
    
    return this.makeRequest(formData);
  }

  // Delete specific files
  async deleteFiles(projectId, fileKeys) {
    const formData = new FormData();
    formData.append('projectId', projectId);
    formData.append('filesToDelete', JSON.stringify(fileKeys));
    formData.append('isUploadFils', 'false');
    
    return this.makeRequest(formData);
  }

  // Delete all files
  async deleteAllFiles(projectId) {
    const formData = new FormData();
    formData.append('projectId', projectId);
    formData.append('isDelete', 'true');
    
    return this.makeRequest(formData);
  }

  // Replace files (delete old + upload new)
  async replaceFiles(projectId, oldKeys, newFiles) {
    const formData = new FormData();
    formData.append('projectId', projectId);
    formData.append('filesToDelete', JSON.stringify(oldKeys));
    formData.append('isUploadFils', 'true');
    
    newFiles.forEach(file => formData.append('dataset', file));
    
    return this.makeRequest(formData);
  }

  // Helper method for API requests
  async makeRequest(formData) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/s3/upload-project-dataset`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        },
        body: formData
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || 'Request failed');
      }
      
      return result;
    } catch (error) {
      console.error('Dataset API Error:', error);
      throw error;
    }
  }
}

// Usage Example
const datasetManager = new DatasetManager('https://api.example.com', 'your-jwt-token');

// Upload files
const files = document.getElementById('fileInput').files;
const result = await datasetManager.uploadFiles('project-uuid', Array.from(files));

// Delete specific files
await datasetManager.deleteFiles('project-uuid', ['file-key-1', 'file-key-2']);

// Replace files
await datasetManager.replaceFiles('project-uuid', ['old-key'], [newFile]);
```

### React Hook Example

```javascript
import { useState, useCallback } from 'react';

export const useDatasetManager = (apiBaseUrl, authToken) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const uploadFiles = useCallback(async (projectId, files, options = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      formData.append('projectId', projectId);
      formData.append('isUploadFils', 'true');
      
      if (options.courseId) formData.append('courseId', options.courseId);
      if (options.isPublic) formData.append('isPublic', options.isPublic);
      
      files.forEach(file => formData.append('dataset', file));
      
      const response = await fetch(`${apiBaseUrl}/api/s3/upload-project-dataset`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${authToken}` },
        body: formData
      });
      
      const result = await response.json();
      
      if (!response.ok) throw new Error(result.message);
      
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiBaseUrl, authToken]);

  return { uploadFiles, loading, error };
};
```

---

## Response Schema

### Success Response
```typescript
interface SuccessResponse {
  success: true;
  message: string;
  data: FileUploadResult[] | OperationResult;
}

interface FileUploadResult {
  url: string;
  key: string;
  bucket: string;
  size: number;
  contentType: string;
  fileName: string;
  isPublic: boolean;
  downloadUrl: string;
}

interface OperationResult {
  message: string;
  files: FileUploadResult[];
}
```

### Error Response
```typescript
interface ErrorResponse {
  success: false;
  error: string;
  message: string;
}
```

---

## Best Practices

1. **File Validation**: Always validate file types and sizes on frontend before upload
2. **Progress Tracking**: Implement upload progress indicators for large files
3. **Error Handling**: Provide clear error messages to users
4. **Batch Operations**: Use replace functionality for efficient file management
5. **Security**: Never expose S3 keys in frontend code
6. **Performance**: Consider file compression for large datasets

---

## Rate Limits
- **Requests**: 100 requests per minute per user
- **File Size**: 200MB per file
- **Total Upload**: 2GB per request

---

## Support
For technical support or questions about this API, contact the development team or refer to the main API documentation.