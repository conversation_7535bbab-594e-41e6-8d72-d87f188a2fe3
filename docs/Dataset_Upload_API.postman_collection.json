{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Dataset Upload API", "description": "Complete collection for Dataset Upload API with all use cases", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Upload New Files", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "{{project_id}}", "type": "text", "description": "UUID of the project"}, {"key": "courseId", "value": "{{course_id}}", "type": "text", "description": "UUID of the course (optional)"}, {"key": "isUploadFils", "value": "true", "type": "text", "description": "Enable file upload"}, {"key": "isPublic", "value": "false", "type": "text", "description": "Make files public"}, {"key": "dataset", "type": "file", "src": [], "description": "Select CSV, JSON, ZIP, Parquet, or IPYNB files"}, {"key": "dataset", "type": "file", "src": [], "description": "Additional file (optional)"}]}, "url": {"raw": "{{base_url}}/api/s3/upload-project-dataset", "host": ["{{base_url}}"], "path": ["api", "s3", "upload-project-dataset"]}, "description": "Upload new dataset files to a project"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "123e4567-e89b-12d3-a456-************", "type": "text"}, {"key": "isUploadFils", "value": "true", "type": "text"}, {"key": "dataset", "type": "file", "src": "data.csv"}]}, "url": {"raw": "https://api.example.com/api/s3/upload-project-dataset", "protocol": "https", "host": ["api", "example", "com"], "path": ["api", "s3", "upload-project-dataset"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Files uploaded successfully\",\n  \"data\": [\n    {\n      \"url\": \"https://s3.amazonaws.com/bucket/project-dataset/user123/data.csv\",\n      \"key\": \"project-dataset/user123/data.csv\",\n      \"bucket\": \"your-bucket\",\n      \"size\": 1024,\n      \"contentType\": \"text/csv\",\n      \"fileName\": \"data.csv\",\n      \"isPublic\": false,\n      \"downloadUrl\": \"https://presigned-url...\"\n    }\n  ]\n}"}]}, {"name": "2. Delete Specific Files", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "{{project_id}}", "type": "text", "description": "UUID of the project"}, {"key": "filesToDelete", "value": "[\"project-dataset/user123/old-file1.csv\", \"project-dataset/user123/old-file2.json\"]", "type": "text", "description": "JSON array of S3 keys to delete"}, {"key": "isUploadFils", "value": "false", "type": "text", "description": "Disable file upload"}]}, "url": {"raw": "{{base_url}}/api/s3/upload-project-dataset", "host": ["{{base_url}}"], "path": ["api", "s3", "upload-project-dataset"]}, "description": "Delete specific files by their S3 keys"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "123e4567-e89b-12d3-a456-************", "type": "text"}, {"key": "filesToDelete", "value": "[\"project-dataset/user123/old-file.csv\"]", "type": "text"}, {"key": "isUploadFils", "value": "false", "type": "text"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Files processed successfully\",\n  \"data\": {\n    \"message\": \"1 files deleted successfully\",\n    \"files\": []\n  }\n}"}]}, {"name": "3. Delete All Files", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "{{project_id}}", "type": "text", "description": "UUID of the project"}, {"key": "isDelete", "value": "true", "type": "text", "description": "Delete all existing files"}]}, "url": {"raw": "{{base_url}}/api/s3/upload-project-dataset", "host": ["{{base_url}}"], "path": ["api", "s3", "upload-project-dataset"]}, "description": "Delete all dataset files from a project"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "123e4567-e89b-12d3-a456-************", "type": "text"}, {"key": "isDelete", "value": "true", "type": "text"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Files processed successfully\",\n  \"data\": {\n    \"message\": \"All dataset files deleted successfully\"\n  }\n}"}]}, {"name": "4. Replace Files (Delete + Upload)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "{{project_id}}", "type": "text", "description": "UUID of the project"}, {"key": "filesToDelete", "value": "[\"project-dataset/user123/old-data.csv\"]", "type": "text", "description": "JSON array of S3 keys to delete"}, {"key": "isUploadFils", "value": "true", "type": "text", "description": "Enable file upload"}, {"key": "dataset", "type": "file", "src": [], "description": "New file to upload"}]}, "url": {"raw": "{{base_url}}/api/s3/upload-project-dataset", "host": ["{{base_url}}"], "path": ["api", "s3", "upload-project-dataset"]}, "description": "Delete specific files and upload new ones in a single request"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "123e4567-e89b-12d3-a456-************", "type": "text"}, {"key": "filesToDelete", "value": "[\"project-dataset/user123/old-data.csv\"]", "type": "text"}, {"key": "isUploadFils", "value": "true", "type": "text"}, {"key": "dataset", "type": "file", "src": "new-data.csv"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Files uploaded successfully\",\n  \"data\": [\n    {\n      \"url\": \"https://s3.amazonaws.com/bucket/project-dataset/user123/new-data.csv\",\n      \"key\": \"project-dataset/user123/new-data.csv\",\n      \"size\": 2048,\n      \"fileName\": \"new-data.csv\",\n      \"isPublic\": false\n    }\n  ]\n}"}]}, {"name": "5. Delete Only (No Upload)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "{{project_id}}", "type": "text", "description": "UUID of the project"}, {"key": "filesToDelete", "value": "[\"file1.csv\", \"file2.json\"]", "type": "text", "description": "JSON array of file keys to delete"}, {"key": "isUploadFils", "value": "false", "type": "text", "description": "Disable file upload"}]}, "url": {"raw": "{{base_url}}/api/s3/upload-project-dataset", "host": ["{{base_url}}"], "path": ["api", "s3", "upload-project-dataset"]}, "description": "Delete files without uploading new ones"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "123e4567-e89b-12d3-a456-************", "type": "text"}, {"key": "filesToDelete", "value": "[\"file1.csv\", \"file2.json\"]", "type": "text"}, {"key": "isUploadFils", "value": "false", "type": "text"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Files processed successfully\",\n  \"data\": {\n    \"message\": \"2 files deleted successfully\",\n    \"files\": []\n  }\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-generate UUID for testing", "if (!pm.environment.get('project_id')) {", "    pm.environment.set('project_id', '123e4567-e89b-12d3-a456-************');", "}", "", "if (!pm.environment.get('course_id')) {", "    pm.environment.set('course_id', '456e7890-e89b-12d3-a456-************');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test for all requests", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}, {"key": "jwt_token", "value": "your-jwt-token-here", "type": "string"}, {"key": "project_id", "value": "123e4567-e89b-12d3-a456-************", "type": "string"}, {"key": "course_id", "value": "456e7890-e89b-12d3-a456-************", "type": "string"}]}